/* 二维码扫描页面样式 */
page {
  background-color: #000;
  height: 100vh;
  overflow: hidden;
}

.container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 扫码区域 */
.scan-area {
  flex: 1;
  position: relative;
  width: 100%;
  background-color: #000;
}

/* 扫码框 */
.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 240px;
  height: 240px;
  z-index: 10;
}

.scan-corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #fff;
}

.scan-corner-tl {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.scan-corner-tr {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.scan-corner-bl {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.scan-corner-br {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #fff, transparent);
  animation: scan-move 2s linear infinite;
}

@keyframes scan-move {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(240px);
  }
}

/* 提示文字 */
.scan-tips {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  font-size: 16px;
  text-align: center;
  z-index: 10;
}

/* 底部操作区 */
.bottom-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  transition: opacity 0.3s ease;
}

.action-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 调试功能样式 */
.debug-panel {
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 10px;
}

.debug-btn {
  margin: 5px 0;
  font-size: 24rpx !important;
  padding: 8rpx 16rpx !important;
  line-height: 1.2 !important;
}

.action-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.flash-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='13 2 3 14 12 14 11 22 21 10 12 10 13 2'%3E%3C/polygon%3E%3C/svg%3E");
}

.flash-icon.active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFD700' stroke='%23FFD700' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='13 2 3 14 12 14 11 22 21 10 12 10 13 2'%3E%3C/polygon%3E%3C/svg%3E");
}

.album-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'%3E%3C/circle%3E%3Cpolyline points='21,15 16,10 5,21'%3E%3C/polyline%3E%3C/svg%3E");
}

.action-text {
  font-size: 12px;
  color: #fff;
}

/* 邀请码绑定弹窗样式 */
.invite-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.invite-modal.show {
  opacity: 1;
  visibility: visible;
}

.invite-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.invite-modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-width: 400px;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 弹窗头部 */
.invite-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.invite-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.invite-modal-close {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #fff;
  border-radius: 15px;
  transition: background-color 0.2s;
}

.invite-modal-close:active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 住户信息区域 */
.invite-resident-info {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
}

.resident-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f0f0f0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.resident-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-text {
  color: #FF9500;
  font-size: 24px;
  font-weight: 600;
}

.resident-details {
  flex: 1;
}

.resident-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.resident-type {
  font-size: 14px;
  color: #666;
  padding: 2px 8px;
  background-color: #E1F0FF;
  color: #007AFF;
  border-radius: 10px;
  display: inline-block;
}

/* 房产列表区域 */
.invite-house-list {
  padding: 20px;
  max-height: 300px;
  overflow: hidden;
}

.house-list-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.house-scroll {
  max-height: 200px;
}

.house-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 12px;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.house-item.bound {
  background-color: #f8f9fa;
  border-color: #e0e0e0;
}

.house-info {
  flex: 1;
}

.house-address {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.house-status {
  font-size: 12px;
}

.status-bound {
  color: #34C759;
  background-color: #E6FFF2;
  padding: 2px 8px;
  border-radius: 6px;
}

.status-pending {
  color: #FF9500;
  background-color: #FFF3E0;
  padding: 2px 8px;
  border-radius: 6px;
}

.house-icon {
  margin-left: 12px;
}

.home-icon {
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z'%3E%3C/path%3E%3Cpath d='M9 22V12h6v10'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 操作按钮区域 */
.invite-actions {
  display: flex;
  padding: 20px;
  gap: 12px;
  background-color: #f8f9fa;
}

.invite-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.invite-btn-cancel {
  background-color: #f0f0f0;
  color: #666;
}

.invite-btn-cancel:active {
  background-color: #e0e0e0;
  transform: scale(0.98);
}

.invite-btn-confirm {
  background: linear-gradient(135deg, #007AFF, #0056b3);
  color: #fff;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.invite-btn-confirm:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 122, 255, 0.2);
}

.invite-btn-confirm:disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
  transform: none;
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 4000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
  padding: 30px;
  border-radius: 12px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #fff;
  font-size: 16px;
}
