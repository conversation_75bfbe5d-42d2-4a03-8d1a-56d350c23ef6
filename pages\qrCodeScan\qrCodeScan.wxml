<!-- 二维码扫描页面 -->
<view class="container">
  <!-- 扫码区域 -->
  <view class="scan-area">
    <camera
      device-position="back"
      flash="{{flashOn ? 'on' : 'off'}}"
      binderror="error"
      style="width: 100%; height: 100%;"
      frame-size="medium"
      bindstop="onCameraStop"
      bindscancode="onScanCode"
      bindinitialdone="onCameraReady"
      scan-area="{{scanArea}}"
      mode="scanCode">
    </camera>
    
    <!-- 扫码框 -->
    <view class="scan-frame">
      <view class="scan-corner scan-corner-tl"></view>
      <view class="scan-corner scan-corner-tr"></view>
      <view class="scan-corner scan-corner-bl"></view>
      <view class="scan-corner scan-corner-br"></view>
      <view class="scan-line"></view>
    </view>
    
    <!-- 提示文字 -->
    <view class="scan-tips">
      <text wx:if="{{cameraReady}}">将二维码放入框内，即可自动扫描</text>
      <text wx:elif="{{!hasPermission}}">请允许使用相机权限</text>
      <text wx:else>正在启动相机...</text>
    </view>
  </view>

  <!-- 底部操作区 -->
  <view class="bottom-actions">
    <view class="action-item {{!cameraReady ? 'disabled' : ''}}" bindtap="toggleFlash">
      <view class="action-icon flash-icon {{flashOn ? 'active' : ''}}"></view>
      <text class="action-text">{{flashOn ? '关闭' : '开启'}}闪光灯</text>
    </view>
    <view class="action-item" bindtap="chooseImage">
      <view class="action-icon album-icon"></view>
      <text class="action-text">从相册选择</text>
    </view>
  </view>
</view>

<!-- 邀请码绑定弹窗 -->
<view class="invite-modal {{showInviteModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
  <view class="invite-modal-mask" bindtap="hideInviteModal"></view>
  <view class="invite-modal-content">
    <!-- 弹窗头部 -->
    <view class="invite-modal-header">
      <text class="invite-modal-title">住户邀请</text>
      <view class="invite-modal-close" bindtap="hideInviteModal">×</view>
    </view>

    <!-- 住户信息区域 -->
    <view class="invite-resident-info" wx:if="{{inviteData}}">
      <view class="resident-avatar">
        <image wx:if="{{inviteData.avatar}}" src="{{inviteData.avatar}}" mode="aspectFill"></image>
        <text wx:else class="avatar-text">{{inviteData.nameInitial || '?'}}</text>
      </view>
      <view class="resident-details">
        <view class="resident-name">{{inviteData.residentName || '未知用户'}}</view>
        <view class="resident-type">{{inviteData.residentTypeText || '住户'}}</view>
      </view>
    </view>

    <!-- 房产列表区域 -->
    <view class="invite-house-list" wx:if="{{inviteData && inviteData.roomList}}">
      <view class="house-list-title">邀请您绑定以下房产：</view>
      <scroll-view class="house-scroll" scroll-y="true">
        <view 
          class="house-item {{item.isBind ? 'bound' : ''}}" 
          wx:for="{{inviteData.roomList}}" 
          wx:key="id"
        >
          <view class="house-info">
            <view class="house-address">{{item.fullAddress || item.address}}</view>
            <view class="house-status">
              <text wx:if="{{item.isBind}}" class="status-bound">已绑定</text>
              <text wx:else class="status-pending">待绑定</text>
            </view>
          </view>
          <view class="house-icon">
            <view class="home-icon"></view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="invite-actions">
      <button class="invite-btn invite-btn-cancel" bindtap="hideInviteModal">取消</button>
      <button 
        class="invite-btn invite-btn-confirm" 
        bindtap="confirmBinding"
        disabled="{{isBinding}}"
      >
        {{isBinding ? '绑定中...' : '确认绑定'}}
      </button>
    </view>
  </view>
</view>

<!-- 加载提示 -->
<view class="loading-overlay {{isLoading ? 'show' : ''}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>

<!-- 调试功能区域 (仅开发环境显示) -->
<view class="debug-panel" style="position: fixed; top: 100px; right: 20px; z-index: 9999;">
  <button class="debug-btn" bindtap="debugCameraStatus" size="mini" type="default">
    相机状态
  </button>
</view>
